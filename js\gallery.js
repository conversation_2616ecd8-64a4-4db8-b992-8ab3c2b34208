// Gallery page JavaScript functionality

document.addEventListener('DOMContentLoaded', function() {
    initGalleryFilter();
    initGalleryModal();
    initLazyLoading();
    initMasonry();
});

// Gallery filter functionality
function initGalleryFilter() {
    const filterButtons = document.querySelectorAll('.filter-buttons .btn');
    const galleryItems = document.querySelectorAll('.gallery-item');
    
    filterButtons.forEach(button => {
        button.addEventListener('click', function() {
            const filter = this.getAttribute('data-filter');
            
            // Update active button
            filterButtons.forEach(btn => btn.classList.remove('active'));
            this.classList.add('active');
            
            // Filter gallery items
            filterGalleryItems(galleryItems, filter);
        });
    });
}

// Filter gallery items based on category
function filterGalleryItems(items, filter) {
    items.forEach((item, index) => {
        const category = item.getAttribute('data-category');
        const shouldShow = filter === 'all' || category === filter;
        
        if (shouldShow) {
            item.style.display = 'block';
            item.style.opacity = '0';
            item.style.transform = 'scale(0.8)';
            
            // Animate in with delay
            setTimeout(() => {
                item.style.transition = 'all 0.4s ease';
                item.style.opacity = '1';
                item.style.transform = 'scale(1)';
            }, index * 50);
        } else {
            item.style.transition = 'all 0.3s ease';
            item.style.opacity = '0';
            item.style.transform = 'scale(0.8)';
            
            setTimeout(() => {
                item.style.display = 'none';
            }, 300);
        }
    });
    
    // Update gallery count
    updateGalleryCount(filter);
}

// Update gallery count display
function updateGalleryCount(filter) {
    const items = document.querySelectorAll('.gallery-item');
    const visibleItems = filter === 'all' ? 
        items.length : 
        document.querySelectorAll(`[data-category="${filter}"]`).length;
    
    // Create or update count display
    let countDisplay = document.querySelector('.gallery-count');
    if (!countDisplay) {
        countDisplay = document.createElement('div');
        countDisplay.className = 'gallery-count text-center mt-3';
        countDisplay.style.cssText = `
            color: #8B4513;
            font-weight: 500;
            font-size: 0.9rem;
        `;
        
        const container = document.querySelector('.gallery-grid .container');
        container.appendChild(countDisplay);
    }
    
    countDisplay.textContent = `Showing ${visibleItems} ${visibleItems === 1 ? 'image' : 'images'}`;
}

// Gallery modal functionality
function initGalleryModal() {
    const galleryModal = document.getElementById('galleryModal');
    const modalImage = document.getElementById('modalImage');
    const modalTitle = document.getElementById('modalTitle');
    const modalDescription = document.getElementById('modalDescription');
    
    if (!galleryModal) return;
    
    // Handle gallery item clicks
    const galleryButtons = document.querySelectorAll('[data-bs-target="#galleryModal"]');
    
    galleryButtons.forEach(button => {
        button.addEventListener('click', function() {
            const imageSrc = this.getAttribute('data-image');
            const title = this.getAttribute('data-title');
            const description = this.getAttribute('data-description');
            
            // Populate modal
            if (modalImage) modalImage.src = imageSrc;
            if (modalTitle) modalTitle.textContent = title;
            if (modalDescription) modalDescription.textContent = description;
            
            // Add navigation if multiple images
            addModalNavigation(this);
        });
    });
    
    // Keyboard navigation
    galleryModal.addEventListener('keydown', function(e) {
        if (e.key === 'ArrowLeft') {
            navigateModal('prev');
        } else if (e.key === 'ArrowRight') {
            navigateModal('next');
        }
    });
}

// Add navigation to modal
function addModalNavigation(currentButton) {
    const modal = document.getElementById('galleryModal');
    const modalBody = modal.querySelector('.modal-body');
    
    // Remove existing navigation
    const existingNav = modal.querySelectorAll('.modal-nav');
    existingNav.forEach(nav => nav.remove());
    
    // Get all gallery buttons in current filter
    const allButtons = Array.from(document.querySelectorAll('[data-bs-target="#galleryModal"]'))
        .filter(btn => {
            const item = btn.closest('.gallery-item');
            return item.style.display !== 'none';
        });
    
    const currentIndex = allButtons.indexOf(currentButton);
    
    if (allButtons.length > 1) {
        // Previous button
        if (currentIndex > 0) {
            const prevBtn = createNavButton('prev', '‹');
            prevBtn.addEventListener('click', () => {
                allButtons[currentIndex - 1].click();
            });
            modalBody.appendChild(prevBtn);
        }
        
        // Next button
        if (currentIndex < allButtons.length - 1) {
            const nextBtn = createNavButton('next', '›');
            nextBtn.addEventListener('click', () => {
                allButtons[currentIndex + 1].click();
            });
            modalBody.appendChild(nextBtn);
        }
        
        // Image counter
        const counter = document.createElement('div');
        counter.className = 'modal-nav image-counter';
        counter.style.cssText = `
            position: absolute;
            bottom: 20px;
            left: 50%;
            transform: translateX(-50%);
            background: rgba(0, 0, 0, 0.7);
            color: white;
            padding: 5px 15px;
            border-radius: 20px;
            font-size: 0.9rem;
        `;
        counter.textContent = `${currentIndex + 1} / ${allButtons.length}`;
        modalBody.appendChild(counter);
    }
}

// Create navigation button
function createNavButton(direction, symbol) {
    const button = document.createElement('button');
    button.className = `modal-nav nav-${direction}`;
    button.innerHTML = symbol;
    button.style.cssText = `
        position: absolute;
        top: 50%;
        ${direction === 'prev' ? 'left: 20px' : 'right: 20px'};
        transform: translateY(-50%);
        background: rgba(139, 69, 19, 0.8);
        color: white;
        border: none;
        width: 50px;
        height: 50px;
        border-radius: 50%;
        font-size: 1.5rem;
        cursor: pointer;
        transition: all 0.3s ease;
        z-index: 10;
    `;
    
    button.addEventListener('mouseenter', function() {
        this.style.background = 'rgba(139, 69, 19, 1)';
        this.style.transform = 'translateY(-50%) scale(1.1)';
    });
    
    button.addEventListener('mouseleave', function() {
        this.style.background = 'rgba(139, 69, 19, 0.8)';
        this.style.transform = 'translateY(-50%) scale(1)';
    });
    
    return button;
}

// Lazy loading for images
function initLazyLoading() {
    const images = document.querySelectorAll('.gallery-card img');
    
    const imageObserver = new IntersectionObserver((entries, observer) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                const img = entry.target;
                
                // Add loading animation
                img.style.opacity = '0';
                img.style.transition = 'opacity 0.3s ease';
                
                // Load image
                const tempImg = new Image();
                tempImg.onload = function() {
                    img.src = this.src;
                    img.style.opacity = '1';
                };
                tempImg.src = img.getAttribute('data-src') || img.src;
                
                observer.unobserve(img);
            }
        });
    });
    
    images.forEach(img => {
        imageObserver.observe(img);
    });
}

// Masonry layout (optional)
function initMasonry() {
    const container = document.getElementById('galleryContainer');
    if (!container) return;
    
    // Simple masonry-like layout
    function arrangeMasonry() {
        const items = container.querySelectorAll('.gallery-item');
        const columns = getColumnCount();
        const columnHeights = new Array(columns).fill(0);
        
        items.forEach(item => {
            if (item.style.display === 'none') return;
            
            // Find shortest column
            const shortestColumn = columnHeights.indexOf(Math.min(...columnHeights));
            
            // Position item
            item.style.position = 'absolute';
            item.style.left = `${(shortestColumn * 100) / columns}%`;
            item.style.top = `${columnHeights[shortestColumn]}px`;
            item.style.width = `${100 / columns}%`;
            
            // Update column height
            columnHeights[shortestColumn] += item.offsetHeight + 20; // 20px gap
        });
        
        // Set container height
        container.style.height = `${Math.max(...columnHeights)}px`;
        container.style.position = 'relative';
    }
    
    function getColumnCount() {
        const width = window.innerWidth;
        // Updated for portrait layout: 3 columns on large screens, 2 on medium, 1 on small
        if (width >= 1200) return 3;
        if (width >= 768) return 2;
        return 1;
    }
    
    // Arrange on load and resize
    window.addEventListener('load', arrangeMasonry);
    window.addEventListener('resize', window.SweetDelights.debounce(arrangeMasonry, 250));
}

// Gallery search functionality
function initGallerySearch() {
    const searchInput = document.createElement('input');
    searchInput.type = 'text';
    searchInput.placeholder = 'Search gallery...';
    searchInput.className = 'form-control mb-3';
    searchInput.style.maxWidth = '300px';
    searchInput.style.margin = '0 auto';
    
    const filterSection = document.querySelector('.gallery-filter .container');
    filterSection.appendChild(searchInput);
    
    searchInput.addEventListener('input', function() {
        const searchTerm = this.value.toLowerCase();
        const galleryItems = document.querySelectorAll('.gallery-item');
        
        galleryItems.forEach(item => {
            const title = item.querySelector('h5').textContent.toLowerCase();
            const description = item.querySelector('p').textContent.toLowerCase();
            
            if (title.includes(searchTerm) || description.includes(searchTerm)) {
                item.style.display = 'block';
            } else {
                item.style.display = 'none';
            }
        });
    });
}

// Fullscreen gallery view
function initFullscreenGallery() {
    const fullscreenBtn = document.createElement('button');
    fullscreenBtn.innerHTML = '<i class="fas fa-expand"></i>';
    fullscreenBtn.className = 'btn btn-outline-light btn-sm';
    fullscreenBtn.style.cssText = `
        position: absolute;
        top: 15px;
        right: 60px;
        z-index: 10;
    `;
    
    const modal = document.getElementById('galleryModal');
    const modalBody = modal.querySelector('.modal-body');
    modalBody.style.position = 'relative';
    modalBody.appendChild(fullscreenBtn);
    
    fullscreenBtn.addEventListener('click', function() {
        const modalImage = document.getElementById('modalImage');
        
        if (modalImage.requestFullscreen) {
            modalImage.requestFullscreen();
        } else if (modalImage.webkitRequestFullscreen) {
            modalImage.webkitRequestFullscreen();
        } else if (modalImage.msRequestFullscreen) {
            modalImage.msRequestFullscreen();
        }
    });
}

// Initialize additional features
document.addEventListener('DOMContentLoaded', function() {
    initGallerySearch();
    initFullscreenGallery();
    
    // Add smooth transitions to gallery items
    const galleryItems = document.querySelectorAll('.gallery-item');
    galleryItems.forEach((item, index) => {
        item.style.transition = 'all 0.3s ease';
        item.style.animationDelay = `${index * 0.1}s`;
    });
});
